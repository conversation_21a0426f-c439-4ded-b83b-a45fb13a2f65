/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../views/store';
import { Chart } from '../../../types';
import {
  sendLoadingMetrics,
  isLoadingMetricsEnabled,
} from '../utils/loadingMetrics';

export interface ChartLoadingInfo {
  chartId: number;
  startTime: number;
  endTime?: number;
  tabId?: string;
}

export interface DashboardLoadingInfo {
  dashboardId: number;
  startTime: number;
  endTime?: number;
  charts: ChartLoadingInfo[];
  currentTab?: string;
  tabSwitches: Array<{
    fromTab?: string;
    toTab: string;
    timestamp: number;
  }>;
}

export const useDashboardLoadingTracker = (
  dashboardId: number,
  currentTabId?: string,
) => {
  const [loadingInfo, setLoadingInfo] = useState<DashboardLoadingInfo>({
    dashboardId,
    startTime: Date.now(),
    charts: [],
    currentTab: currentTabId,
    tabSwitches: [],
  });

  const charts = useSelector((state: RootState) => state.charts);
  const chartIds = useSelector((state: RootState) =>
    Object.values(state.charts).map((chart: Chart) => chart.id),
  );
  const renderedChartIds = useSelector((state: RootState) =>
    Object.values(state.charts)
      .filter((chart: Chart) => chart.chartStatus === 'rendered')
      .map((chart: Chart) => chart.id),
  );

  const prevRenderedChartIds = useRef<number[]>([]);
  const prevTabId = useRef<string | undefined>(currentTabId);
  const dashboardStartTime = useRef<number>(Date.now());
  const hasInitialized = useRef<boolean>(false);

  // Check if dashboard loading is complete
  const isDashboardLoaded = useCallback(() => {
    if (chartIds.length === 0) return false;

    // All charts should be rendered
    const allChartsRendered = chartIds.every(chartId =>
      renderedChartIds.includes(chartId),
    );

    // All tracked charts should have end times
    const allTrackedChartsComplete = loadingInfo.charts
      .filter(chart => chartIds.includes(chart.chartId))
      .every(chart => chart.endTime);

    return allChartsRendered && allTrackedChartsComplete;
  }, [chartIds, renderedChartIds, loadingInfo.charts]);

  // const trackChartStart = useCallback(
  //   (chartId: number) => {
  //     setLoadingInfo(prev => {
  //       const existingChart = prev.charts.find(c => c.chartId === chartId);
  //       if (existingChart) return prev;

  //       return {
  //         ...prev,
  //         charts: [
  //           ...prev.charts,
  //           {
  //             chartId,
  //             startTime: Date.now(),
  //             tabId: currentTabId,
  //           },
  //         ],
  //       };
  //     });
  //   },
  //   [currentTabId],
  // );

  // const trackChartEnd = useCallback((chartId: number) => {
  //   setLoadingInfo(prev => ({
  //     ...prev,
  //     charts: prev.charts.map(chart =>
  //       chart.chartId === chartId && !chart.endTime
  //         ? { ...chart, endTime: Date.now() }
  //         : chart,
  //     ),
  //   }));
  // }, []);

  // Initialize dashboard loading tracking
  useEffect(() => {
    if (!hasInitialized.current) {
      dashboardStartTime.current = Date.now();
      setLoadingInfo(prev => ({
        ...prev,
        startTime: dashboardStartTime.current,
        currentTab: currentTabId,
      }));
      hasInitialized.current = true;
    }
  }, [currentTabId]);

  // Listen to chart loading events
  useEffect(() => {
    const handleChartLoadingStart = (event: CustomEvent) => {
      const { chartId, startTime, tabId } = event.detail;
      setLoadingInfo(prev => {
        const existingChart = prev.charts.find(c => c.chartId === chartId);
        if (existingChart) return prev;

        return {
          ...prev,
          charts: [
            ...prev.charts,
            {
              chartId,
              startTime,
              tabId,
            },
          ],
        };
      });
    };

    const handleChartLoadingEnd = (event: CustomEvent) => {
      const { chartId, endTime } = event.detail;
      setLoadingInfo(prev => ({
        ...prev,
        charts: prev.charts.map(chart =>
          chart.chartId === chartId && !chart.endTime
            ? { ...chart, endTime }
            : chart,
        ),
      }));
    };

    window.addEventListener(
      'chart-loading-start',
      handleChartLoadingStart as EventListener,
    );
    window.addEventListener(
      'chart-loading-end',
      handleChartLoadingEnd as EventListener,
    );

    return () => {
      window.removeEventListener(
        'chart-loading-start',
        handleChartLoadingStart as EventListener,
      );
      window.removeEventListener(
        'chart-loading-end',
        handleChartLoadingEnd as EventListener,
      );
    };
  }, []);

  // Track tab switches
  useEffect(() => {
    if (prevTabId.current !== currentTabId && hasInitialized.current) {
      const timestamp = Date.now();
      setLoadingInfo(prev => ({
        ...prev,
        currentTab: currentTabId,
        tabSwitches: [
          ...prev.tabSwitches,
          {
            fromTab: prevTabId.current,
            toTab: currentTabId || 'default',
            timestamp,
          },
        ],
      }));
      prevTabId.current = currentTabId;
    }
  }, [currentTabId]);

  // Track chart loading states
  useEffect(() => {
    const newlyStartedCharts: ChartLoadingInfo[] = [];
    const updatedCharts: ChartLoadingInfo[] = [];

    Object.values(charts).forEach((chart: Chart) => {
      const existingChart = loadingInfo.charts.find(
        c => c.chartId === chart.id,
      );

      if (chart.chartStatus === 'loading' && !existingChart) {
        // Chart started loading
        newlyStartedCharts.push({
          chartId: chart.id,
          startTime: chart.chartUpdateStartTime || Date.now(),
          tabId: currentTabId,
        });
      } else if (
        chart.chartStatus === 'rendered' &&
        existingChart &&
        !existingChart.endTime
      ) {
        // Chart finished loading
        updatedCharts.push({
          ...existingChart,
          endTime: chart.chartUpdateEndTime || Date.now(),
        });
      }
    });

    if (newlyStartedCharts.length > 0 || updatedCharts.length > 0) {
      setLoadingInfo(prev => {
        const updatedChartsMap = new Map(
          updatedCharts.map(c => [c.chartId, c]),
        );
        const existingCharts = prev.charts.map(
          chart => updatedChartsMap.get(chart.chartId) || chart,
        );

        return {
          ...prev,
          charts: [...existingCharts, ...newlyStartedCharts],
        };
      });
    }
  }, [charts, currentTabId, loadingInfo.charts]);

  // Send metrics when dashboard loading is complete
  useEffect(() => {
    if (isDashboardLoaded() && !loadingInfo.endTime) {
      const endTime = Date.now();
      const finalLoadingInfo: DashboardLoadingInfo = {
        ...loadingInfo,
        endTime,
      };

      setLoadingInfo(finalLoadingInfo);
      console.log('finalLoadingInfo', finalLoadingInfo);

      // Send metrics to endpoint
      // sendLoadingMetrics(finalLoadingInfo).catch(() => {
      //   // Error is already logged in sendLoadingMetrics function
      // });
    }
  }, [isDashboardLoaded, loadingInfo]);

  // Track when new charts are rendered
  useEffect(() => {
    const newlyRenderedCharts = renderedChartIds.filter(
      id => !prevRenderedChartIds.current.includes(id),
    );

    if (newlyRenderedCharts.length > 0) {
      prevRenderedChartIds.current = renderedChartIds;
    }
  }, [renderedChartIds]);

  // Early return if metrics are disabled
  // if (!isLoadingMetricsEnabled()) {
  //   return {
  //     loadingInfo: null,
  //     isDashboardLoaded: false,
  //     trackChartStart: () => {},
  //     trackChartEnd: () => {},
  //   };
  // }

  // return {
  //   loadingInfo,
  //   isDashboardLoaded: isDashboardLoaded(),
  //   trackChartStart,
  //   trackChartEnd,
  // };
};
