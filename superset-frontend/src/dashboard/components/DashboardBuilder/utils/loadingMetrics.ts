/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

import { SupersetClient } from '@superset-ui/core';
import { DashboardLoadingInfo } from '../hooks/useDashboardLoadingTracker';

export interface LoadingMetricsPayload {
  dashboardId: number;
  totalLoadTime: number;
  dashboardStartTime: number;
  dashboardEndTime: number;
  charts: Array<{
    chartId: number;
    loadTime: number;
    startTime: number;
    endTime: number;
    tabId?: string;
  }>;
  tabSwitches: Array<{
    fromTab?: string;
    toTab: string;
    timestamp: number;
  }>;
  metadata: {
    userAgent: string;
    url: string;
    timestamp: number;
    totalCharts: number;
    chartsPerTab: Record<string, number>;
  };
}

/**
 * Get the endpoint URL for sending loading metrics
 * This can be configured via environment variables or dashboard settings
 */
const getLoadingMetricsEndpoint = (): string => {
  // Check if endpoint is configured in bootstrap data
  const bootstrapData = (window as any)?.bootstrapData;
  const endpoint =
    bootstrapData?.common?.conf?.DASHBOARD_LOADING_METRICS_ENDPOINT;
  if (endpoint) {
    return endpoint;
  }

  // Default endpoint - you can modify this based on your backend setup
  return '/api/v1/dashboard_loading_metrics/';
};

/**
 * Calculate loading metrics from dashboard loading info
 */
const calculateMetrics = (
  loadingInfo: DashboardLoadingInfo,
): LoadingMetricsPayload => {
  const { dashboardId, startTime, endTime, charts, tabSwitches } = loadingInfo;

  if (!endTime) {
    throw new Error('Dashboard loading is not complete');
  }

  const totalLoadTime = endTime - startTime;

  const chartMetrics = charts
    .filter(chart => chart.endTime) // Only include completed charts
    .map(chart => ({
      chartId: chart.chartId,
      loadTime: chart.endTime! - chart.startTime,
      startTime: chart.startTime,
      endTime: chart.endTime!,
      tabId: chart.tabId,
    }));

  // Calculate charts per tab
  const chartsPerTab: Record<string, number> = {};
  chartMetrics.forEach(chart => {
    const tabId = chart.tabId || 'default';
    chartsPerTab[tabId] = (chartsPerTab[tabId] || 0) + 1;
  });

  return {
    dashboardId,
    totalLoadTime,
    dashboardStartTime: startTime,
    dashboardEndTime: endTime,
    charts: chartMetrics,
    tabSwitches,
    metadata: {
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: Date.now(),
      totalCharts: chartMetrics.length,
      chartsPerTab,
    },
  };
};

/**
 * Send loading metrics to the configured endpoint
 */
export const sendLoadingMetrics = async (
  loadingInfo: DashboardLoadingInfo,
): Promise<void> => {
  try {
    const endpoint = getLoadingMetricsEndpoint();
    const payload = calculateMetrics(loadingInfo);

    // Use logging instead of console for production
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Sending dashboard loading metrics:', payload);
    }

    await SupersetClient.post({
      endpoint,
      jsonPayload: payload,
    });

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Dashboard loading metrics sent successfully');
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to send dashboard loading metrics:', error);

    // Optionally, store metrics locally for retry later
    try {
      const storedMetrics =
        localStorage.getItem('dashboard_loading_metrics') || '[]';
      const metrics = JSON.parse(storedMetrics);
      metrics.push({
        ...calculateMetrics(loadingInfo),
        failed: true,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Keep only last 10 failed metrics to avoid localStorage bloat
      const recentMetrics = metrics.slice(-10);
      localStorage.setItem(
        'dashboard_loading_metrics',
        JSON.stringify(recentMetrics),
      );
    } catch (storageError) {
      // eslint-disable-next-line no-console
      console.error('Failed to store metrics locally:', storageError);
    }

    throw error;
  }
};

/**
 * Retry sending failed metrics stored in localStorage
 */
export const retryFailedMetrics = async (): Promise<void> => {
  try {
    const storedMetrics = localStorage.getItem('dashboard_loading_metrics');
    if (!storedMetrics) return;

    const metrics = JSON.parse(storedMetrics);
    const failedMetrics = metrics.filter((m: any) => m.failed);

    if (failedMetrics.length === 0) return;

    const endpoint = getLoadingMetricsEndpoint();

    // Process metrics sequentially to avoid overwhelming the server
    const retryPromises = failedMetrics.map(async (metric: any) => {
      try {
        // Remove failed flag and error before sending
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { failed, error, ...cleanMetric } = metric;

        await SupersetClient.post({
          endpoint,
          jsonPayload: cleanMetric,
        });

        if (process.env.NODE_ENV === 'development') {
          // eslint-disable-next-line no-console
          console.log('Retry: Dashboard loading metrics sent successfully');
        }
        return true;
      } catch (retryError) {
        // eslint-disable-next-line no-console
        console.error('Retry failed for metric:', retryError);
        return false;
      }
    });

    await Promise.allSettled(retryPromises);

    // Clear failed metrics after retry attempt
    const successfulMetrics = metrics.filter((m: any) => !m.failed);
    localStorage.setItem(
      'dashboard_loading_metrics',
      JSON.stringify(successfulMetrics),
    );
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to retry sending metrics:', error);
  }
};

/**
 * Check if loading metrics feature is enabled
 */
export const isLoadingMetricsEnabled = (): boolean => {
  // Check if feature is enabled in bootstrap data
  const bootstrapData = (window as any)?.bootstrapData;
  const isEnabled =
    bootstrapData?.common?.conf?.DASHBOARD_LOADING_METRICS_ENABLED;

  if (isEnabled === false) {
    return false;
  }

  // Check if endpoint is configured
  try {
    const endpoint = getLoadingMetricsEndpoint();
    return Boolean(endpoint);
  } catch {
    return false;
  }
};
