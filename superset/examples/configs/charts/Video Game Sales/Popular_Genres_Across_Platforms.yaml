# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Popular Genres Across Platforms
viz_type: heatmap
params:
  adhoc_filters: []
  all_columns_x: platform
  all_columns_y: genre
  bottom_margin: auto
  canvas_image_rendering: pixelated
  datasource: 64__table
  granularity_sqla: year
  left_margin: auto
  linear_color_scheme: blue_white_yellow
  metric: count
  normalize_across: heatmap
  queryFields:
    metric: metrics
  row_limit: 10000
  show_legend: true
  show_perc: true
  show_values: true
  sort_x_axis: alpha_asc
  sort_y_axis: alpha_asc
  time_range: No filter
  url_params: {}
  viz_type: heatmap
  xscale_interval: null
  y_axis_bounds:
  - null
  - null
  y_axis_format: SMART_NUMBER
  yscale_interval: null
cache_timeout: null
uuid: 326fc7e5-b7f1-448e-8a6f-80d0e7ce0b64
version: 1.0.0
dataset_uuid: 53d47c0c-c03d-47f0-b9ac-81225f808283
