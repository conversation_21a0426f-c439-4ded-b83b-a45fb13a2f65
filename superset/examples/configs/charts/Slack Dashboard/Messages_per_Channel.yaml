# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Messages per Channel
viz_type: area
params:
  adhoc_filters:
  - clause: WHERE
    comparator: github-notifications
    expressionType: SIMPLE
    filterOptionName: filter_7ud3u2eujnw_1pmeehxvw0b
    isExtra: false
    isNew: false
    operator: '!='
    sqlExpression: null
    subject: name
  annotation_layers: []
  bottom_margin: auto
  color_scheme: supersetColors
  comparison_type: values
  datasource: 56__table
  granularity_sqla: ts
  groupby:
  - name
  label_colors:
    '0': '#1FA8C9'
    '1': '#454E7C'
    announcements: '#A868B7'
    apache-releases: '#666666'
    beginners: '#666666'
    commits: '#E04355'
    community-feedback: '#EFA1AA'
    contributing: '#8FD3E4'
    cypress-tests: '#FDE380'
    dashboard-filters: '#FCC700'
    dashboard-level-access: '#D1C6BC'
    dashboards: '#3CCCCB'
    design: '#1FA8C9'
    developers: '#9EE5E5'
    embedded-dashboards: '#ACE1C4'
    feature-requests: '#454E7C'
    general: '#3CCCCB'
    github-notifications: '#E04355'
    globalnav_search: '#A1A6BD'
    graduation: '#A1A6BD'
    helm-k8-deployment: '#FEC0A1'
    introductions: '#5AC189'
    jobs: '#FF7F44'
    localization: '#5AC189'
    newsletter: '#FF7F44'
    product_feedback: '#D3B3DA'
    pull-requests: '#A38F79'
    superset-champions: '#FCC700'
    superset_prod_reports: '#A868B7'
    superset_stage_alerts: '#A38F79'
    support: '#8FD3E4'
    visualization_plugins: '#B2B2B2'
  limit: 10
  line_interpolation: linear
  metrics:
  - count
  min_periods: 0
  order_desc: true
  queryFields:
    groupby: groupby
    metrics: metrics
  rich_tooltip: true
  rolling_periods: 14
  rolling_type: mean
  row_limit: 1000
  show_brush: auto
  show_controls: false
  show_legend: true
  slice_id: 2395
  stacked_style: stream
  time_grain_sqla: P1D
  time_range: No filter
  url_params: {}
  viz_type: area
  x_axis_format: smart_date
  x_axis_showminmax: true
  x_ticks_layout: auto
  y_axis_bounds:
  - 0
  - null
  y_axis_format: SMART_NUMBER
  y_log_scale: false
cache_timeout: null
uuid: b0f11bdf-793f-473f-b7d5-b9265e657896
version: 1.0.0
dataset_uuid: 6e533506-fce6-4f6a-b116-d139df6dbdea
