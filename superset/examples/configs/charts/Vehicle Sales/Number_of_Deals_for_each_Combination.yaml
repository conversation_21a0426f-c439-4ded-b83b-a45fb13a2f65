# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Number of Deals (for each Combination)
viz_type: heatmap
params:
  adhoc_filters: []
  all_columns_x: deal_size
  all_columns_y: product_line
  bottom_margin: 100
  canvas_image_rendering: pixelated
  datasource: 23__table
  granularity_sqla: order_date
  left_margin: 75
  linear_color_scheme: schemePuBuGn
  metric: count
  normalize_across: heatmap
  normalized: true
  queryFields:
    metric: metrics
  row_limit: null
  show_legend: true
  show_perc: true
  show_values: true
  slice_id: 2810
  sort_x_axis: alpha_asc
  sort_y_axis: alpha_asc
  time_range: No filter
  url_params: {}
  viz_type: heatmap
  xscale_interval: null
  y_axis_bounds:
  - null
  - null
  y_axis_format: SMART_NUMBER
  yscale_interval: null
cache_timeout: null
uuid: bd20fc69-dd51-46c1-99b5-09e37a434bf1
version: 1.0.0
dataset_uuid: e8623bb9-5e00-f531-506a-19607f5f8005
