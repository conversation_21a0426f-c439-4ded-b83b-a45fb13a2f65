# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: Ethnic Minority & Gender
viz_type: sankey
params:
  adhoc_filters:
  - clause: WHERE
    comparator: 'NULL'
    expressionType: SIMPLE
    filterOptionName: filter_of9xf5uks2_5pisp1se9r5
    isExtra: false
    isNew: false
    operator: '!='
    sqlExpression: null
    subject: ethnic_minority
  - clause: WHERE
    comparator: 'NULL'
    expressionType: SIMPLE
    filterOptionName: filter_9ikn7htywfm_2579he7pk5x
    isExtra: false
    isNew: false
    operator: '!='
    sqlExpression: null
    subject: gender
  color_scheme: supersetColors
  datasource: 42__table
  granularity_sqla: time_start
  groupby:
  - ethnic_minority
  - gender
  label_colors: {}
  metric: count
  queryFields:
    groupby: groupby
    metric: metrics
  row_limit: null
  time_range: No filter
  url_params: {}
  viz_type: sankey
cache_timeout: null
uuid: 4880e4f4-b701-4be0-86f3-e7e89432e83b
version: 1.0.0
dataset_uuid: d95a2865-53ce-1f82-a53d-8e3c89331469
