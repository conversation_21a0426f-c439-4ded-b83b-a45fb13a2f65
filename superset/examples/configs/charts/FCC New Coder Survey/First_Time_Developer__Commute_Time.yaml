# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
slice_name: First Time Developer & Commute Time
viz_type: sankey
params:
  adhoc_filters:
  - clause: WHERE
    comparator: '1'
    expressionType: SIMPLE
    filterOptionName: filter_9hkcdqhiqor_84pk01t2k9
    isExtra: false
    isNew: false
    operator: ==
    sqlExpression: null
    subject: is_software_dev
  - clause: WHERE
    comparator: 'NULL'
    expressionType: SIMPLE
    filterOptionName: filter_d5l1qwsthl_okyuouvmors
    isExtra: false
    isNew: false
    operator: '!='
    sqlExpression: null
    subject: first_time_developer
  - clause: WHERE
    comparator: 'NULL'
    expressionType: SIMPLE
    filterOptionName: filter_95548uvadi_f990s8nzl4
    isExtra: false
    isNew: false
    operator: '!='
    sqlExpression: null
    subject: communite_time
  color_scheme: supersetColors
  datasource: 42__table
  granularity_sqla: time_start
  groupby:
  - first_time_developer
  - communite_time
  label_colors: {}
  metric: count
  queryFields:
    groupby: groupby
    metric: metrics
  row_limit: 10000
  time_range: No filter
  url_params: {}
  viz_type: sankey
cache_timeout: null
uuid: 067c4a1e-ae03-4c0c-8e2a-d2c0f4bf43c3
version: 1.0.0
dataset_uuid: d95a2865-53ce-1f82-a53d-8e3c89331469
