{#
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
#}
{% extends "appbuilder/baselayout.html" %}
{% from 'superset/partials/asset_bundle.html' import css_bundle, js_bundle with context %}

{% block head_css %}
  {{ super() }}
  {% set favicons = appbuilder.app.config['FAVICONS'] %}
  {% for favicon in favicons %}
    <link
      rel="{{favicon.rel if favicon.rel else "icon"}}"
      type="{{favicon.type if favicon.type else "image/png"}}"
      {% if favicon.sizes %}sizes={{favicon.sizes}}{% endif %}
      href="{{ "" if favicon.href.startswith("http") else assets_prefix }}{{favicon.href}}"
    >
  {% endfor %}
  {{ css_bundle("theme") }}
{% endblock %}

{% block head_js %}
  {{ super() }}
  {{ js_bundle("theme") }}
{% endblock %}

{% block tail_js %}
  {{ super() }}
  {{ js_bundle("preamble") }}
  {{ js_bundle('menu') }}
  {% include "tail_js_custom_extra.html" %}
{% endblock %}
