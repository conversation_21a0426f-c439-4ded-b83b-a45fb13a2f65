# Документация по интерфейсу командной строки (CLI) в DODO

## Содержание

1. [Введение](#введение)
2. [Архитектура](#архитектура)
3. [Стандартная функциональность](#стандартная-функциональность)
4. [DODO-специфичная функциональность](#dodo-специфичная-функциональность)
5. [Основные команды](#основные-команды)
6. [Примеры использования](#примеры-использования)

## Введение

Модуль `cli` предоставляет интерфейс командной строки (Command Line Interface, CLI) для управления и администрирования Superset. CLI позволяет выполнять различные административные задачи, такие как инициализация базы данных, загрузка примеров, обновление метаданных, экспорт и импорт дашбордов и многое другое.

В DODO этот модуль используется для автоматизации задач администрирования и развертывания Superset, а также для выполнения регулярных операций обслуживания.

## Архитектура

Модуль состоит из следующих основных компонентов:

1. **Основной модуль** (`cli/main.py`):
   - Определяет основную группу команд `superset`
   - Загружает и регистрирует все подкоманды из других модулей

2. **Модули команд**:
   - `cli/examples.py` - команды для загрузки примеров
   - `cli/importexport.py` - команды для экспорта и импорта
   - `cli/reset.py` - команды для сброса Superset
   - `cli/test.py` - команды для тестирования
   - `cli/update.py` - команды для обновления метаданных
   - `cli/viz_migrations.py` - команды для миграции визуализаций
   - и другие

3. **Вспомогательные функции** (`cli/lib.py`):
   - Утилиты и вспомогательные функции для команд CLI

## Стандартная функциональность

Стандартная функциональность модуля `cli` включает:

1. **Управление базой данных**:
   - Инициализация базы данных
   - Обновление схемы базы данных
   - Сброс базы данных

2. **Управление примерами**:
   - Загрузка примеров дашбордов и данных
   - Загрузка тестовых пользователей

3. **Экспорт и импорт**:
   - Экспорт дашбордов, графиков и источников данных
   - Импорт дашбордов, графиков и источников данных

4. **Обновление метаданных**:
   - Синхронизация тегов
   - Обновление документации API

5. **Тестирование**:
   - Загрузка тестовых пользователей
   - Загрузка тестовых данных

## DODO-специфичная функциональность

В результате анализа кода **не обнаружено DODO-специфичных изменений или расширений** в модуле `cli`. Весь код в этом модуле является стандартным для Superset.

Однако, в файле `docker-init.sh` есть некоторые настройки, которые могут быть специфичны для DODO, такие как настройка пароля администратора и загрузка примеров. Эти настройки не являются частью модуля `cli`, но используют его команды.

Кроме того, в директории `.dodo` есть файл `README.md`, который содержит инструкции по настройке и запуску Superset для разработки в DODO. Этот файл также не является частью модуля `cli`, но содержит информацию о том, как использовать CLI в контексте DODO.

## Основные команды

### Инициализация и обновление

```bash
# Инициализация Superset
superset init

# Обновление схемы базы данных
superset db upgrade

# Создание администратора
superset fab create-admin --username admin --firstname Superset --lastname Admin --email <EMAIL> --password admin
```

### Загрузка примеров

```bash
# Загрузка всех примеров
superset load_examples

# Загрузка только метаданных примеров
superset load_examples --only-metadata

# Загрузка тестовых данных
superset load_examples --load-test-data
```

### Экспорт и импорт

```bash
# Экспорт дашборда
superset export_dashboards -f dashboards.json

# Импорт дашборда
superset import_dashboards -p dashboards.json

# Экспорт графиков
superset export_charts -f charts.json

# Импорт графиков
superset import_charts -p charts.json
```

### Обновление метаданных

```bash
# Синхронизация тегов
superset sync_tags

# Обновление документации API
superset update_api_docs
```

### Сброс Superset

```bash
# Сброс Superset (требует включения флага ENABLE_FACTORY_RESET_COMMAND)
superset factory_reset --username admin
```

## Примеры использования

### Инициализация Superset при первом запуске

```bash
# Обновление схемы базы данных
superset db upgrade

# Создание администратора
superset fab create-admin --username admin --firstname Superset --lastname Admin --email <EMAIL> --password admin

# Инициализация ролей и разрешений
superset init

# Загрузка примеров
superset load_examples
```

### Обновление Superset после изменения схемы

```bash
# Обновление схемы базы данных
superset db upgrade

# Обновление ролей и разрешений
superset init
```

### Экспорт и импорт дашбордов между окружениями

```bash
# Экспорт дашбордов из одного окружения
superset export_dashboards -f dashboards.json

# Импорт дашбордов в другое окружение
superset import_dashboards -p dashboards.json
```
