# Документация по расширенным типам данных (Advanced Data Type) в DODO

## Содержание

1. [Введение](#введение)
2. [Архитектура](#архитектура)
3. [Существующие типы данных](#существующие-типы-данных)
4. [DODO-специфичная функциональность](#dodo-специфичная-функциональность)
5. [Техническая реализация](#техническая-реализация)
6. [API](#api)

## Введение

Модуль `advanced_data_type` предоставляет механизм для работы с расширенными типами данных в Superset. Этот механизм позволяет определять специальные типы данных, которые могут быть использованы для фильтрации и отображения данных в интерфейсе пользователя.

## Архитектура

Модуль состоит из следующих основных компонентов:

1. **Типы данных** (`types.py`):
   - `AdvancedDataTypeRequest` - класс запроса для преобразования значений
   - `AdvancedDataTypeResponse` - класс ответа с преобразованными значениями
   - `AdvancedDataType` - основной класс для определения расширенных типов данных

2. **API** (`api.py`):
   - `AdvancedDataTypeRestApi` - REST API для работы с расширенными типами данных
   - Эндпоинты для получения списка доступных типов и преобразования значений

3. **Схемы** (`schemas.py`):
   - `AdvancedDataTypeSchema` - схема для сериализации/десериализации данных
   - `advanced_data_type_convert_schema` - схема для валидации запросов на преобразование

4. **Плагины** (папка `plugins`):
   - Реализации конкретных расширенных типов данных
   - Каждый плагин определяет свои функции преобразования и фильтрации

## Существующие типы данных

В стандартной поставке Superset имеются следующие расширенные типы данных:

1. **Internet Address** (`internet_address.py`):
   - Представляет IP-адреса и CIDR-диапазоны
   - Поддерживает фильтрацию по IP-адресам и диапазонам

2. **Internet Port** (`internet_port.py`):
   - Представляет сетевые порты
   - Поддерживает фильтрацию по номерам портов и их именам (например, "http" -> 80)

## DODO-специфичная функциональность

**На основе анализа кодовой базы не обнаружено DODO-специфичных расширений или модификаций в модуле `advanced_data_type`.**

В текущей реализации модуля `advanced_data_type` не найдено никаких DODO-специфичных изменений, расширений или кастомизаций. Модуль используется в стандартном виде, как он поставляется в оригинальной версии Superset.

Хотя в других частях кодовой базы (например, в `superset-frontend/src/DodoExtensions`) имеются DODO-специфичные компоненты и функциональность, в модуле `advanced_data_type` таких изменений не обнаружено.

## Техническая реализация

### Определение нового типа данных

Для определения нового расширенного типа данных необходимо:

1. Создать функцию преобразования типа (`translate_type`), которая принимает `AdvancedDataTypeRequest` и возвращает `AdvancedDataTypeResponse`
2. Создать функцию фильтрации (`translate_filter`), которая преобразует значения в SQL-выражения
3. Создать экземпляр класса `AdvancedDataType` с указанием этих функций
4. Зарегистрировать тип в конфигурации Superset (`ADVANCED_DATA_TYPES`)

### Пример определения типа данных

```python
def my_type_func(req: AdvancedDataTypeRequest) -> AdvancedDataTypeResponse:
    # Логика преобразования значений
    return {
        "values": [...],
        "display_value": "...",
        "valid_filter_operators": [...],
        "error_message": None
    }

def my_type_filter_func(col: Column, operator: FilterOperator, values: Any) -> BinaryExpression:
    # Логика создания SQL-выражения для фильтрации
    return col == values[0]

my_type: AdvancedDataType = AdvancedDataType(
    verbose_name="my type",
    description="description of my type",
    valid_data_types=["string"],
    translate_filter=my_type_filter_func,
    translate_type=my_type_func,
)
```

## API

### Получение списка доступных типов

```
GET /api/v1/advanced_data_type/types
```

Возвращает список доступных расширенных типов данных.

### Преобразование значений

```
GET /api/v1/advanced_data_type/convert?q={...}
```

Параметры запроса:
- `type`: тип данных для преобразования
- `values`: массив значений для преобразования

Возвращает объект `AdvancedDataTypeResponse` с преобразованными значениями.
